<?php

namespace App\Modules\Transfer\Requests;

use App\Modules\Transfer\Services\TransferService;
use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'orderby' => ['string'],
            'email' => ['string'],
            'status' => ['string'],
        ];
    }

    public function show()
    {
        $filters = [
            'orderby' => $this->input('orderby'),
            'email' => $this->input('email'),
            'status' => $this->input('status'),
        ];

        return [
            'items' => TransferService::instance()->get($filters),
            'filters' => $filters,
        ];
    }
}
