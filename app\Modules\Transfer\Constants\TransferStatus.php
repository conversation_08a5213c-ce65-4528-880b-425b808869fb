<?php

namespace App\Modules\Transfer\Constants;

final class TransferStatus
{
    public const PENDING = 'pending';
    public const APPROVED = 'approved';
    public const REJECTED = 'rejected';
    public const COMPLETED = 'completed';
    public const CANCELLED = 'cancelled';
    public const FAILED = 'failed';

    public static function all()
    {
        return [
            self::PENDING,
            self::APPROVED,
            self::REJECTED,
            self::COMPLETED,
            self::CANCELLED,
            self::FAILED,
        ];
    }

    public static function getDisplayName($status)
    {
        $displayNames = [
            self::PENDING => 'Pending',
            self::APPROVED => 'Approved',
            self::REJECTED => 'Rejected',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
            self::FAILED => 'Failed',
        ];

        return $displayNames[$status] ?? ucfirst($status);
    }
}
