<?php

namespace App\Modules\Transfer\Services;

use Illuminate\Support\Facades\DB;
use App\Modules\Transfer\Constants\TransferStatus;

class TransferService
{
    public static function instance(): self
    {
        return new self;
    }

    public function get(array $filters = []): array
    {
        $query = DB::table('transfer_domains')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('users', 'users.id', '=', 'registered_domains.user_contact_registrar_id')
            ->select([
                'transfer_domains.id',
                'transfer_domains.registered_domain_id',
                'transfer_domains.status',
                'transfer_domains.error_code',
                'transfer_domains.error_message',
                'transfer_domains.deleted_at',
                'transfer_domains.created_at',
                'transfer_domains.updated_at',
                'domains.name as domain',
                'users.email as user_email',
                'users.first_name',
                'users.last_name',
            ])
            ->whereNull('transfer_domains.deleted_at');

        // Apply filters
        if (!empty($filters['email'])) {
            $query->where('users.email', 'like', '%' . $filters['email'] . '%');
        }

        if (!empty($filters['status'])) {
            $query->where('transfer_domains.status', $filters['status']);
        }

        // Apply ordering
        if (!empty($filters['orderby'])) {
            $orderParts = explode(':', $filters['orderby']);
            $column = $orderParts[0];
            $direction = $orderParts[1] ?? 'desc';

            switch ($column) {
                case 'domain':
                    $query->orderBy('domains.name', $direction);
                    break;
                case 'date_created':
                    $query->orderBy('transfer_domains.created_at', $direction);
                    break;
                default:
                    $query->orderBy('transfer_domains.created_at', 'desc');
            }
        } else {
            $query->orderBy('transfer_domains.created_at', 'desc');
        }

        $transfers = $query->get()->toArray();

        // Format the data for frontend
        return array_map(function ($transfer) {
            return [
                'id' => $transfer->id,
                'domain' => $transfer->domain,
                'user' => trim($transfer->first_name . ' ' . $transfer->last_name),
                'user_email' => $transfer->user_email,
                'date_created' => date('Y-m-d H:i:s', strtotime($transfer->created_at)),
                'status' => TransferStatus::getDisplayName($transfer->status),
                'raw_status' => $transfer->status,
                'error_code' => $transfer->error_code,
                'error_message' => $transfer->error_message,
            ];
        }, $transfers);
    }
}
