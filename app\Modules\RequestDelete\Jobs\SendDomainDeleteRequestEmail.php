<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Mail\UserDeleteRequestMail;
use App\Events\EmailSentEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Constant\QueueConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Exception;
use Throwable;

class SendDomainDeleteRequestEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $data;
    private string $subject;
    private string $body;

    public $tries = 3;
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(array $data, string $subject, string $body)
    {
        $this->data = $data;
        $this->subject = $subject;
        $this->body = $body;

        $this->onConnection(QueueConnection::EMAIL);
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (!isset($this->data['userEmail']) || !isset($this->data['domainName'])) {
                app(AuthLogger::class)->error('SendDomainDeleteRequestEmail: Missing required data (userEmail or domainName)');
                return;
            }

            $message = [
                'subject'  => $this->subject,
                'greeting' => 'Greetings!',
                'body'     => $this->body,
                'text'     => Carbon::now()->format('Y-m-d H:i:s'),
                'sender'   => 'StrangeDomains Support',
            ];

            Mail::to($this->data['userEmail'])->send(new UserDeleteRequestMail($message));

            event(new EmailSentEvent(
                $this->data['userId'],
                $this->data['userName'],
                $this->data['userEmail'],
                $this->subject,
                'Domain Deletion Request',
                json_encode($message),
                null
            ));

            app(AuthLogger::class)->info("Domain deletion request email sent successfully to {$this->data['userEmail']} for domain {$this->data['domainName']}");

        } catch (Exception $e) {
            app(AuthLogger::class)->error("Failed to send domain deletion request email to {$this->data['userEmail']}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error("SendDomainDeleteRequestEmail job failed for {$this->data['userEmail']}: " . $exception->getMessage());
    }

    /**
     * Get the backoff delays for retries.
     */
    public function backoff(): array
    {
        return [30, 60, 120];
    }
}
