import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { MdMoreVert } from "react-icons/md";

export default function Item({ item, onApproveTransfer }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const getStatusColor = (status) => {
        const colors = {
            Pending: "bg-yellow-100 text-yellow-800",
            Approved: "bg-green-100 text-green-800",
            Rejected: "bg-red-100 text-red-800",
            Completed: "bg-blue-100 text-blue-800",
            Cancelled: "bg-gray-100 text-gray-800",
            Failed: "bg-red-100 text-red-800",
        };
        return colors[status] || "bg-gray-100 text-gray-800";
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    return (
        <tr>
            <td>
                <span className="font-semibold text-gray-900">
                    {item.domain}
                </span>
            </td>
            <td>
                <div className="flex flex-col">
                    <span className="text-gray-900">{item.user}</span>
                </div>
            </td>
            <td>
                <span className="text-gray-700">
                    {formatDate(item.date_created)}
                </span>
            </td>
            <td>
                <span
                    className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                        item.status
                    )}`}
                >
                    {item.status}
                </span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center p-1 rounded-full hover:bg-gray-200 transition-colors"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="text-xl text-gray-600" />
                    </button>
                    <DropDownContainer show={show}>
                        {item.raw_status === "pending" && (
                            <>
                                <button
                                    className="text-green-600 px-4 py-2 hover:bg-gray-100 justify-start flex w-full text-left"
                                    onClick={() => {
                                        onApproveTransfer(item);
                                        setShow(false);
                                    }}
                                >
                                    Approve
                                </button>
                            </>
                        )}
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
